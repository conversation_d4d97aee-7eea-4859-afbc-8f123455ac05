import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from './core/ChatService';
import { GeminiLLMService } from './services/GeminiLLMService';
import { LLMServiceFactory } from './services/LLMService';
import { WebInterface } from './interfaces/WebInterface';
import { StreamingChatService } from './streaming';
import { SessionCleanupService } from './core/SessionCleanupService';
import { ForaChat } from './operations';
import config, { validateConfig } from './config';
import WebSocket from 'ws';
import http from 'http';
import fs from 'fs';
import path from 'path';
// Import operations to ensure workflow queues are initialized before DBOS launch
import './operations';

export class ForaChatApp {
  private chatService: ChatService;
  private webInterface: WebInterface;
  private streamingService: StreamingChatService;
  private server: http.Server | null = null;
  private wss: WebSocket.Server | null = null;
  private isInitialized: boolean = false;
  private quietMode: boolean = false;
  private originalConsoleLog: typeof console.log | null = null;
  private originalConsoleInfo: typeof console.info | null = null;
  private originalConsoleWarn: typeof console.warn | null = null;

  constructor(options: { quietMode?: boolean } = {}) {
    // Validate configuration
    validateConfig();

    // Set quiet mode
    this.quietMode = options.quietMode || false;

    // If in quiet mode, suppress console output immediately and persistently
    if (this.quietMode) {
      this.suppressConsoleOutput();
    }

    // Register LLM providers
    LLMServiceFactory.register('gemini', () => new GeminiLLMService());

    // Initialize services
    const llmService = LLMServiceFactory.create('gemini');
    this.chatService = new ChatService(llmService);
    this.webInterface = new WebInterface(this.chatService);
    this.streamingService = new StreamingChatService(this.chatService);
  }

  private log(message: string): void {
    if (!this.quietMode) {
      // Use the original console.log to bypass any suppression
      if (this.originalConsoleLog) {
        this.originalConsoleLog(message);
      } else {
        console.log(message);
      }
    } else {
      // In quiet mode, write to log file if available, otherwise suppress completely
      if ((this as any).logStream) {
        (this as any).logStream.write(message + '\n');
      }
      // Otherwise, suppress completely (do nothing)
    }
  }

  private suppressConsoleOutput(): void {
    // Store original console methods
    this.originalConsoleLog = console.log;
    this.originalConsoleInfo = console.info;
    this.originalConsoleWarn = console.warn;

    // Replace with no-op functions
    console.log = () => {};
    console.info = () => {};
    console.warn = () => {};

    // Create a log file for server output when in quiet mode
    const logFile = path.join(process.cwd(), 'server.log');
    const logStream = fs.createWriteStream(logFile, { flags: 'a' });

    // Redirect stdout and stderr to log file instead of suppressing completely
    const originalStdoutWrite = process.stdout.write;
    const originalStderrWrite = process.stderr.write;

    process.stdout.write = (chunk: any, encoding?: any, callback?: any) => {
      logStream.write(chunk, encoding, callback);
      return true;
    };

    process.stderr.write = (chunk: any, encoding?: any, callback?: any) => {
      logStream.write(chunk, encoding, callback);
      return true;
    };

    // Store original methods for restoration
    (this as any).originalStdoutWrite = originalStdoutWrite;
    (this as any).originalStderrWrite = originalStderrWrite;
    (this as any).logStream = logStream;
  }

  private restoreConsoleOutput(): void {
    // Restore original console methods
    if (this.originalConsoleLog) {
      console.log = this.originalConsoleLog;
    }
    if (this.originalConsoleInfo) {
      console.info = this.originalConsoleInfo;
    }
    if (this.originalConsoleWarn) {
      console.warn = this.originalConsoleWarn;
    }

    // Restore stdout and stderr
    if ((this as any).originalStdoutWrite) {
      process.stdout.write = (this as any).originalStdoutWrite;
    }
    if ((this as any).originalStderrWrite) {
      process.stderr.write = (this as any).originalStderrWrite;
    }

    // Close log stream
    if ((this as any).logStream) {
      (this as any).logStream.end();
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Configure DBOS
    DBOS.setConfig({
      name: "forachat",
      databaseUrl: config.database.url,
      userDbclient: "knex" as any
    });

    // Launch DBOS with the web interface
    await DBOS.launch({ expressApp: this.webInterface.getApp() });
    this.log("✅ DBOS Launched successfully");

    // Start session cleanup service (runs every hour)
    SessionCleanupService.startCleanupService(60);
    this.log("✅ Session cleanup service started");

    this.isInitialized = true;
  }

  async start(): Promise<void> {
    await this.initialize();

    const app = this.webInterface.getApp();

    // Create HTTP server
    this.server = http.createServer(app);

    // Create WebSocket server
    this.wss = new WebSocket.Server({ server: this.server });

    // Handle WebSocket connections
    this.wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.server.listen(config.port, () => {
      this.log(`🚀 ForaChat server is running on http://localhost:${config.port}`);
      this.log(`📊 Environment: ${config.environment}`);
      this.log(`🤖 LLM Provider: ${config.llm.model}`);
      this.log('\n📚 Available endpoints:');
      this.log('  GET  / - Web UI');
      this.log('  POST /chat - Send a message');
      this.log('  GET  /health - Health check');
      this.log('  POST /conversation/:id/message - Continue conversation');
      this.log('  GET  /conversation/:id - Get conversation history');
      this.log('  WebSocket - Real-time chat interface');
    });
  }

  getChatService(): ChatService {
    return this.chatService;
  }

  getWebInterface(): WebInterface {
    return this.webInterface;
  }

  getStreamingService(): StreamingChatService {
    return this.streamingService;
  }

  // Method to allow external code to restore console output if needed
  restoreConsole(): void {
    if (this.quietMode) {
      this.restoreConsoleOutput();
    }
  }

  // Method to re-suppress console output if needed
  suppressConsole(): void {
    if (this.quietMode) {
      this.suppressConsoleOutput();
    }
  }

  private async handleWebSocketConnection(ws: WebSocket, req: http.IncomingMessage): Promise<void> {
    try {
      // Parse cookies from WebSocket request
      const cookies = this.parseCookies(req.headers.cookie || '');
      let sessionId = cookies.forachat_session;
      let session = null;
      const userIdentifier = req.socket.remoteAddress || 'unknown';

      this.log(`🔗 WebSocket connection attempt from ${userIdentifier}, cookie sessionId: ${sessionId ? sessionId.substring(0, 8) + '...' : 'none'}`);

      if (sessionId) {
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();

        if (session) {
          this.log(`✅ WebSocket reusing existing session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
        } else {
          this.log(`❌ WebSocket session ${sessionId.substring(0, 8)}... not found or expired, creating new session`);
        }
      } else {
        this.log(`🆕 WebSocket no session cookie found, creating new session for ${userIdentifier}`);
      }

      if (!session) {
        // Create new session for WebSocket
        sessionId = this.generateSessionId();

        const sessionRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web' as const,
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.socket.remoteAddress
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;
        this.log(`🆕 Created new WebSocket session ${sessionId.substring(0, 8)}... for user ${session.user_identifier}`);
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      this.streamingService.createSession(sessionId, ws, session);
    } catch (error) {
      if (!this.quietMode) {
        console.error(`Error handling WebSocket connection: ${(error as Error).message}`);
      }
      ws.close(1011, 'Internal server error');
    }
  }

  private parseCookies(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  async shutdown(): Promise<void> {
    // Restore console output before shutdown messages
    if (this.quietMode) {
      this.restoreConsoleOutput();
    }

    this.log('🛑 Shutting down ForaChat...');

    // Stop session cleanup service
    SessionCleanupService.stopCleanupService();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
    }

    // Close HTTP server
    if (this.server) {
      this.server.close();
    }

    process.exit(0);
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});
